<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversa WhatsApp - <%= data.atendimento?.cliente_info?.[0]?.nome || 'Cliente' %></title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #e5ddd5;
            color: #333;
            line-height: 1.4;
        }
        .no-print {
            display: block;
            margin-bottom: 20px;
            text-align: center;
        }
        .print-btn {
            background: #25d366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .print-btn:hover {
            background: #1ea952;
        }
        .header {
            background: #25d366;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .header-info {
            margin-top: 15px;
            font-size: 14px;
            line-height: 1.6;
        }
        .chat-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            min-height: 400px;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
        }
        .message.client {
            justify-content: flex-start;
        }
        .message.agent {
            justify-content: flex-end;
        }
        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        .message.client .message-content {
            background-color: #ffffff;
            border-bottom-left-radius: 5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .message.agent .message-content {
            background-color: #dcf8c6;
            border-bottom-right-radius: 5px;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        .message-header strong {
            font-size: 14px;
        }
        .message.client .message-header strong {
            color: #1976d2;
        }
        .message.agent .message-header strong {
            color: #25d366;
        }
        .message-time {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }
        .message-text {
            font-size: 14px;
            line-height: 1.4;
            color: #333;
        }
        .date-separator {
            text-align: center;
            margin: 20px 0;
        }
        .date-badge {
            background-color: rgba(0,0,0,0.1);
            color: #666;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            display: inline-block;
        }
        .message-file {
            margin-top: 8px;
            padding: 8px;
            background-color: rgba(0,0,0,0.05);
            border-radius: 5px;
            font-style: italic;
            color: #666;
            font-size: 12px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
            page-break-inside: avoid;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            body { 
                background: white !important; 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .header {
                background: #25d366 !important;
                color: white !important;
            }
            .message.agent .message-content {
                background-color: #dcf8c6 !important;
            }
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-btn" onclick="window.print()">🖨️ Imprimir / Salvar como PDF</button>
        <button class="print-btn" onclick="window.close()">❌ Fechar</button>
    </div>

    <div class="header">
        <h1>📱 Conversa WhatsApp - OPA Viewer</h1>
        <div class="header-info">
            <strong>Cliente:</strong> <%= data.atendimento?.cliente_info?.[0]?.nome || 'Cliente não identificado' %><br>
            <strong>Atendente:</strong> <%= data.atendimento?.atendente_info?.[0]?.nome || 'Atendente não identificado' %><br>
            <strong>WhatsApp:</strong> <%= data.atendimento?.canal_cliente || '' %><br>
            <strong>Data:</strong> <%= new Date(data.atendimento?.date).toLocaleString('pt-BR') %><br>
            <strong>Protocolo:</strong> <%= data.atendimento?.protocolo || 'N/A' %><br>
            <strong>Total de mensagens:</strong> <%= data.mensagens.length %>
        </div>
    </div>
    
    <div class="chat-container">
        <% 
        let dataAnterior = '';
        data.mensagens.forEach(msg => {
            const dataMsg = new Date(msg.data);
            const dataFormatada = dataMsg.toLocaleDateString('pt-BR');
            const horaFormatada = dataMsg.toLocaleTimeString('pt-BR', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            
            // Separador de data
            if (dataFormatada !== dataAnterior) { %>
                <div class="date-separator">
                    <span class="date-badge"><%= dataFormatada %></span>
                </div>
            <% 
                dataAnterior = dataFormatada;
            }
            
            const isCliente = msg.usuario_info && msg.usuario_info.length > 0;
            const nomeRemetente = isCliente ? 
                msg.usuario_info[0].nome : 
                (msg.atendente_info?.[0]?.nome || 'Atendente');
        %>
            <div class="message <%= isCliente ? 'client' : 'agent' %>">
                <div class="message-content">
                    <div class="message-header">
                        <strong><%= nomeRemetente %></strong>
                        <span class="message-time"><%= horaFormatada %></span>
                    </div>
                    <% if (msg.mensagem && typeof msg.mensagem === 'string' && msg.mensagem.trim()) { %>
                        <div class="message-text"><%= msg.mensagem %></div>
                    <% } %>
                    <% if (msg.arquivo_info && msg.arquivo_info.length > 0) { %>
                        <div class="message-file">
                            📎 Arquivo: <%= msg.arquivo_info[0].nome %>
                            <% if (msg.arquivo_info[0].size) { %>
                                (<%= Math.round(msg.arquivo_info[0].size / 1024) %> KB)
                            <% } %>
                        </div>
                    <% } %>
                    <% if (!msg.mensagem && (!msg.arquivo_info || msg.arquivo_info.length === 0)) { %>
                        <div class="message-text">[Mensagem sem conteúdo]</div>
                    <% } %>
                </div>
            </div>
        <% }); %>
    </div>
    
    <div class="footer">
        Relatório gerado em <%= new Date().toLocaleString('pt-BR') %> - OPA Viewer
    </div>

    <script>
        // Auto-abrir diálogo de impressão
        window.addEventListener('load', function() {
            // Aguardar um pouco para garantir que a página carregou completamente
            setTimeout(() => {
                window.print();
            }, 1500);
        });
    </script>
</body>
</html>
