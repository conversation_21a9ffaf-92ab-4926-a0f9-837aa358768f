# OPA Viewer - Visualizador de Arquivo Morto WhatsApp

Uma aplicação web completa em Node.js para visualizar conversas arquivadas do WhatsApp armazenadas no MongoDB.

## 🚀 Funcionalidades

- **Busca por Data**: Encontre conversas de um período específico
- **Busca por WhatsApp**: Digite o número (com ou sem formatação)
- **Busca por Nome**: Busque pelo nome do cliente
- **Busca por Protocolo**: Digite o protocolo (OPA é opcional)
- **Interface de Chat**: Visualização estilo WhatsApp das conversas (perspectiva do atendente)
- **Suporte a Arquivos**: Visualização e download de imagens, vídeos e documentos
- **Exportação PDF**: Exporte conversas completas para PDF estilizado
- **Memória de Busca**: Mantém a última busca realizada
- **Responsivo**: Funciona em desktop e mobile

## 📋 Pré-requisitos

- Node.js (versão 14 ou superior)
- MongoDB rodando em `localhost:27017`
- Base de dados `suite` com as collections:
  - `atendimentos`
  - `atendimentos_mensagens`
  - `clientes_users`
  - `usuarios`

## 🔧 Instalação

1. Clone ou baixe o projeto
2. Instale as dependências:
```bash
npm install
```

3. Inicie o servidor:
```bash
npm start
```

4. Acesse no navegador:
```
http://localhost:8500
```

## 🎯 Como Usar

### Página Principal
1. Selecione o tipo de busca (Data, WhatsApp ou Nome)
2. Preencha os campos necessários
3. Clique em "Buscar"
4. Os resultados aparecerão na lista à direita

### Visualizar Conversa
1. Clique em qualquer resultado da busca ou no botão "Ver Conversa"
2. Uma nova aba abrirá com a conversa completa
3. As mensagens são exibidas em ordem cronológica
4. **Perspectiva do Atendente**: Mensagens do cliente à esquerda (branco), atendente à direita (verde)
5. Arquivos são exibidos como imagens, vídeos ou links para download
6. Use o botão "PDF" para exportar a conversa

### Exportar para PDF
1. **Na lista de resultados**: Clique no botão "PDF" ao lado de "Ver Conversa"
2. **Na conversa**: Clique no botão "PDF" no cabeçalho
3. O PDF será gerado e baixado automaticamente com formatação profissional

## 📊 Estrutura do Projeto

```
opa-viewer/
├── server.js              # Servidor principal
├── package.json           # Dependências
├── views/                 # Templates EJS
│   ├── index.ejs         # Página de busca
│   └── chat.ejs          # Visualização do chat
├── public/               # Arquivos estáticos
│   ├── css/
│   │   ├── style.css     # Estilos da página principal
│   │   └── chat.css      # Estilos do chat
│   └── js/
│       └── app.js        # JavaScript da aplicação
└── README.md             # Este arquivo
```

## 🔌 API Endpoints

- `GET /` - Página principal de busca
- `POST /api/buscar` - Buscar atendimentos
- `GET /api/mensagens/:id` - Buscar mensagens de um atendimento
- `GET /chat/:id` - Visualizar chat de um atendimento
- `GET /arquivo/:id` - Servir arquivos (imagens, documentos, etc.)
- `GET /api/arquivo/:id` - Informações do arquivo
- `GET /pdf/chat/:id` - Exportar conversa para PDF

## 🎨 Tecnologias Utilizadas

- **Backend**: Node.js, Express.js
- **Database**: MongoDB
- **Frontend**: Bootstrap 5, Font Awesome, EJS
- **PDF**: Puppeteer para geração de PDFs
- **Utilitários**: Moment.js para datas

## 🔍 Exemplos de Busca

### Por Data
- Data início: 2020-03-25
- Data fim: 2020-03-26 (opcional)

### Por WhatsApp
- `5535991125771`
- `(35) 99112-5771`
- `35991125771`

### Por Nome
- `Marco A. Pio`
- `Valdenea`
- `Natalia Moreti`

### Por Protocolo
- `OPA20205`
- `20205` (OPA será adicionado automaticamente)
- `opa2024123`

## 🐛 Solução de Problemas

### Erro de Conexão MongoDB
- Verifique se o MongoDB está rodando
- Confirme se a base `suite` existe
- Teste a conexão: `mongo localhost:27017/suite`

### Nenhum resultado encontrado
- Verifique se os dados existem no banco
- Teste com diferentes critérios de busca
- Verifique os logs do servidor no console

## 📝 Desenvolvimento

Para desenvolvimento com auto-reload:
```bash
npm run dev
```

## 🤝 Contribuição

Sinta-se à vontade para contribuir com melhorias, correções ou novas funcionalidades!

## 📄 Licença

MIT License
