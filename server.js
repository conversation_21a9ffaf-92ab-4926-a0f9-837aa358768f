const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const { MongoClient, ObjectId } = require('mongodb');
const moment = require('moment');
const puppeteer = require('puppeteer');

const app = express();
const PORT = process.env.PORT || 8500;

// Configuração do MongoDB
const MONGO_URL = 'mongodb://localhost:27017';
const DB_NAME = 'suite';

let db;

// Middleware
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Conectar ao MongoDB
async function connectToMongo() {
    try {
        const client = new MongoClient(MONGO_URL);
        await client.connect();
        db = client.db(DB_NAME);
        console.log('Conectado ao MongoDB com sucesso!');
    } catch (error) {
        console.error('Erro ao conectar ao MongoDB:', error);
        process.exit(1);
    }
}

// Rota principal - página de busca
app.get('/', (req, res) => {
    res.render('index');
});

// API para buscar atendimentos
app.post('/api/buscar', async (req, res) => {
    try {
        const { tipo_busca, valor_busca, data_inicio, data_fim } = req.body;

        let query = {};
        let useAggregation = false;

        // Construir query baseada no tipo de busca
        if (tipo_busca === 'data' && data_inicio) {
            const inicio = new Date(data_inicio);
            const fim = data_fim ? new Date(data_fim) : new Date(data_inicio);
            fim.setHours(23, 59, 59, 999); // Final do dia

            query.date = {
                $gte: inicio,
                $lte: fim
            };
        } else if (tipo_busca === 'whatsapp' && valor_busca) {
            // Buscar por número de WhatsApp (pode estar em canal_cliente)
            query.canal_cliente = new RegExp(valor_busca.replace(/\D/g, ''), 'i');
        } else if (tipo_busca === 'protocolo' && valor_busca) {
            // Buscar por protocolo (adicionar OPA se não tiver)
            let protocoloBusca = valor_busca.trim();
            if (!protocoloBusca.toUpperCase().startsWith('OPA')) {
                protocoloBusca = 'OPA' + protocoloBusca;
            }
            query.protocolo = new RegExp(protocoloBusca, 'i');
        } else if (tipo_busca === 'nome' && valor_busca) {
            useAggregation = true;
        }

        let atendimentos;

        if (useAggregation || tipo_busca === 'nome') {
            // Usar agregação para busca por nome (com lookup correto)
            const pipeline = [
                {
                    $lookup: {
                        from: 'clientes_users',
                        localField: 'id_user',
                        foreignField: '_id',
                        as: 'cliente_info'
                    }
                },
                {
                    $lookup: {
                        from: 'usuarios',
                        localField: 'id_atendente',
                        foreignField: '_id',
                        as: 'atendente_info'
                    }
                }
            ];

            // Adicionar filtros específicos
            if (tipo_busca === 'nome' && valor_busca) {
                pipeline.push({
                    $match: {
                        'cliente_info.nome': new RegExp(valor_busca, 'i')
                    }
                });
            } else {
                pipeline.push({ $match: query });
            }

            pipeline.push(
                { $sort: { date: -1 } },
                { $limit: 50 }
            );

            atendimentos = await db.collection('atendimentos').aggregate(pipeline).toArray();
        } else {
            // Buscar atendimentos com informações do cliente (método tradicional)
            atendimentos = await db.collection('atendimentos').aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: 'clientes_users',
                        localField: 'id_user',
                        foreignField: '_id',
                        as: 'cliente_info'
                    }
                },
                {
                    $lookup: {
                        from: 'usuarios',
                        localField: 'id_atendente',
                        foreignField: '_id',
                        as: 'atendente_info'
                    }
                },
                { $sort: { date: -1 } },
                { $limit: 50 }
            ]).toArray();
        }

        res.json(atendimentos);

    } catch (error) {
        console.error('Erro na busca:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// API para buscar mensagens de um atendimento
app.get('/api/mensagens/:id_atendimento', async (req, res) => {
    try {
        const { id_atendimento } = req.params;

        // Buscar mensagens do atendimento com arquivos
        const mensagens = await db.collection('atendimentos_mensagens').aggregate([
            {
                $match: {
                    id_rota: new ObjectId(id_atendimento)
                }
            },
            {
                $lookup: {
                    from: 'clientes_users',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'usuario_info'
                }
            },
            {
                $lookup: {
                    from: 'usuarios',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'atendente_info'
                }
            },
            {
                $lookup: {
                    from: 'arquivos',
                    localField: 'objeto',
                    foreignField: '_id',
                    as: 'arquivo_info'
                }
            },
            { $sort: { data: 1 } }
        ]).toArray();

        // Buscar informações do atendimento com cliente e atendente
        const atendimento = await db.collection('atendimentos').aggregate([
            {
                $match: { _id: new ObjectId(id_atendimento) }
            },
            {
                $lookup: {
                    from: 'clientes_users',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'cliente_info'
                }
            },
            {
                $lookup: {
                    from: 'usuarios',
                    localField: 'id_atendente',
                    foreignField: '_id',
                    as: 'atendente_info'
                }
            }
        ]).toArray();

        res.json({
            atendimento: atendimento[0] || null,
            mensagens
        });

    } catch (error) {
        console.error('Erro ao buscar mensagens:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// Rota para visualizar chat
app.get('/chat/:id_atendimento', (req, res) => {
    res.render('chat', { id_atendimento: req.params.id_atendimento });
});

// Rota para servir arquivos
app.get('/arquivo/:id_arquivo', async (req, res) => {
    try {
        const { id_arquivo } = req.params;
        const arquivo = await db.collection('arquivos').findOne({
            _id: new ObjectId(id_arquivo)
        });

        if (!arquivo) {
            return res.status(404).json({ error: 'Arquivo não encontrado' });
        }

        const fs = require('fs');
        const path = require('path');
        const filePath = path.join(__dirname, arquivo.local);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({ error: 'Arquivo físico não encontrado' });
        }

        res.setHeader('Content-Type', arquivo.tipo);
        res.setHeader('Content-Disposition', `inline; filename="${arquivo.nome}"`);
        res.sendFile(filePath);

    } catch (error) {
        console.error('Erro ao servir arquivo:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// API para informações do arquivo
app.get('/api/arquivo/:id_arquivo', async (req, res) => {
    try {
        const { id_arquivo } = req.params;
        const arquivo = await db.collection('arquivos').findOne({
            _id: new ObjectId(id_arquivo)
        });

        if (!arquivo) {
            return res.status(404).json({ error: 'Arquivo não encontrado' });
        }

        res.json(arquivo);

    } catch (error) {
        console.error('Erro ao buscar arquivo:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// Rota para exportar conversa para PDF
app.get('/pdf/chat/:id_atendimento', async (req, res) => {
    try {
        const { id_atendimento } = req.params;

        // Buscar dados da conversa
        const response = await fetch(`http://localhost:${PORT}/api/mensagens/${id_atendimento}`);
        const data = await response.json();

        if (data.error) {
            return res.status(404).json({ error: data.error });
        }

        // Gerar HTML para PDF
        const html = gerarHTMLChat(data);

        // Gerar PDF com Puppeteer
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();
        await page.setContent(html, { waitUntil: 'networkidle0' });

        const pdf = await page.pdf({
            format: 'A4',
            margin: {
                top: '20px',
                right: '20px',
                bottom: '20px',
                left: '20px'
            },
            printBackground: true
        });

        await browser.close();

        const clienteNome = data.atendimento?.cliente_info?.[0]?.nome || 'Cliente';
        const dataAtendimento = new Date(data.atendimento?.date).toLocaleDateString('pt-BR');
        const filename = `Conversa_${clienteNome.replace(/[^a-zA-Z0-9]/g, '_')}_${dataAtendimento.replace(/\//g, '-')}.pdf`;

        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(pdf);

    } catch (error) {
        console.error('Erro ao gerar PDF:', error);
        res.status(500).json({ error: 'Erro ao gerar PDF' });
    }
});

function gerarHTMLChat(data) {
    const clienteNome = data.atendimento?.cliente_info?.[0]?.nome || 'Cliente não identificado';
    const atendente = data.atendimento?.atendente_info?.[0]?.nome || 'Atendente não identificado';
    const whatsapp = data.atendimento?.canal_cliente || '';
    const dataAtendimento = new Date(data.atendimento?.date).toLocaleString('pt-BR');
    const protocolo = data.atendimento?.protocolo || 'N/A';

    let mensagensHTML = '';
    let dataAnterior = '';

    data.mensagens.forEach(msg => {
        const data = new Date(msg.data);
        const dataFormatada = data.toLocaleDateString('pt-BR');
        const horaFormatada = data.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Separador de data
        if (dataFormatada !== dataAnterior) {
            mensagensHTML += `
                <div style="text-align: center; margin: 20px 0; color: #666; font-size: 12px;">
                    <span style="background: #f0f0f0; padding: 5px 15px; border-radius: 15px;">
                        ${dataFormatada}
                    </span>
                </div>
            `;
            dataAnterior = dataFormatada;
        }

        const isCliente = msg.usuario_info && msg.usuario_info.length > 0;
        const nomeRemetente = isCliente ?
            msg.usuario_info[0].nome :
            (msg.atendente_info?.[0]?.nome || 'Atendente');

        const alignStyle = isCliente ? 'margin-left: 20%; text-align: left;' : 'margin-right: 20%; text-align: left;';
        const bgColor = isCliente ? '#ffffff' : '#dcf8c6';
        const nameColor = isCliente ? '#1976d2' : '#25d366';

        mensagensHTML += `
            <div style="${alignStyle} margin-bottom: 15px;">
                <div style="background: ${bgColor}; padding: 10px 15px; border-radius: 18px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                        <strong style="color: ${nameColor}; font-size: 14px;">${nomeRemetente}</strong>
                        <span style="color: #666; font-size: 12px;">${horaFormatada}</span>
                    </div>
                    <div style="color: #333; line-height: 1.4;">
                        ${msg.mensagem || '[Mensagem sem conteúdo]'}
                    </div>
                    ${msg.arquivo_info && msg.arquivo_info.length > 0 ?
                        `<div style="margin-top: 8px; color: #666; font-style: italic;">
                            📎 Arquivo: ${msg.arquivo_info[0].nome}
                        </div>` : ''
                    }
                </div>
            </div>
        `;
    });

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Conversa WhatsApp - ${clienteNome}</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: #e5ddd5;
                }
                .header {
                    background: #25d366;
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                }
                .chat-container {
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    min-height: 500px;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1 style="margin: 0; font-size: 24px;">
                    📱 Conversa WhatsApp - OPA Viewer
                </h1>
                <div style="margin-top: 10px; font-size: 16px;">
                    <strong>Cliente:</strong> ${clienteNome}<br>
                    <strong>Atendente:</strong> ${atendente}<br>
                    <strong>WhatsApp:</strong> ${whatsapp}<br>
                    <strong>Data:</strong> ${dataAtendimento}<br>
                    <strong>Protocolo:</strong> ${protocolo}<br>
                    <strong>Total de mensagens:</strong> ${data.mensagens.length}
                </div>
            </div>

            <div class="chat-container">
                ${mensagensHTML}
            </div>

            <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
                Relatório gerado em ${new Date().toLocaleString('pt-BR')} - OPA Viewer
            </div>
        </body>
        </html>
    `;
}

// Iniciar servidor
connectToMongo().then(() => {
    app.listen(PORT, () => {
        console.log(`Servidor rodando na porta ${PORT}`);
        console.log(`Acesse: http://localhost:${PORT}`);
    });
});
