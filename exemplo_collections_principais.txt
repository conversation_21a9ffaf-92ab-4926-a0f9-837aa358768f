// Documento da collection atendimentos:
{
  "_id": {
    "$oid": "5e7bdf5f4ba1c907197c6bac"
  },
  "id_atendimento": 0,
  "tags": [],
  "usuarios_acompanhar": [],
  "id_atendente": {
    "$oid": "5e6fbeb7285b4a0873a8d44d"
  },
  "id_user": {
    "$oid": "5e7bdf5f4ba1c907197c6bab"
  },
  "canal": "whatsapp",
  "canal_cliente": "<EMAIL>",
  "canal_id": "5e7a1f08285b4a0873a8d458",
  "setor": {
    "$oid": "5bf73d1d186f7d2b0d647a60"
  },
  "descricao": "",
  "status": "F",
  "date": {
    "$date": "2020-03-25T22:46:55.355Z"
  },
  "inicio": {
    "$date": "2020-03-26T14:53:22.230Z"
  },
  "fim": {
    "$date": "2020-03-26T14:53:24.867Z"
  },
  "update": {
    "$date": "2020-03-26T14:53:24.876Z"
  },
  "protocolo": "OPA20205",
  "data_fixacao": null,
  "__v": 0,
  "atend_fixado_topo": null,
  "updatedAt": {
    "$date": "2023-01-03T11:20:43.352Z"
  }
}

// Documento da collection atendimentos_mensagens:
{
    "_id": {
      "$oid": "5e7be0114ba1c907197c6bb1"
    },
    "id_rota": {
      "$oid": "5e7bdf5f4ba1c907197c6bac"
    },
    "id_user": {
      "$oid": "5e7bdf5f4ba1c907197c6bab"
    },
    "mensagem": "Gostaria de saber como funciona o plano para tv de vcs?",
    "tipo": null,
    "id_mensagem_citada": null,
    "coord": null,
    "objeto": null,
    "chamada": null,
    "data": {
      "$date": "2020-03-25T22:49:53.414Z"
    },
    "idMensagemWhatsApp": null,
    "idMensagemCitadaWhatsApp": null,
    "__v": 0
  }

// Documento da collection clientes_users:
  {
    "_id": {
      "$oid": "5e7c96334ba1c907197c6bd6"
    },
    "nome": "Marco A. Pio De Magalhães",
    "whatsapp": "<EMAIL>",
    "fones": [],
    "__v": 1,
    "email_principal": null,
    "emails": [],
    "habilitarAlerta": true,
    "historico_email": true,
    "opt_in_opt_out": [],
    "cli_emp": {
      "$oid": "60257eea698b706fea062587"
    },
    "classificacao": "",
    "updatedAt": {
      "$date": "2023-01-03T11:20:48.288Z"
    }
  }

// Documento da collection clientes:
{
    "_id": {
      "$oid": "6006e306c485430bc7537c76"
    },
    "tags": [],
    "id": "32586",
    "id_filial": "2",
    "nome": "VALDENEA GONCALVES DIAS",
    "fantasia": "VALDENEA GONCALVES DIAS",
    "cliente": true,
    "prospect": false,
    "cpf_cnpj": "04141782629",
    "status": "I",
    "latitude": "-21.8025632",
    "longitude": "-46.5145067",
    "chaves": [],
    "__v": 0,
    "ultima_atualizacao": "2024-07-29 14:07:01",
    "endereco": "R JOAO VERONEZI, 74 - Jd Philadélphia, Poços de Caldas-MG",
    "updatedAt": {
      "$date": "2024-07-29T18:00:02.308Z"
    }
  }

// Documento da collection usuarios:
  {
    "_id": {
      "$oid": "5d1642434b16a50312cc8f43"
    },
    "empresa": {
      "$oid": "5bf73d1c186f7d2b0d647a5f"
    },
    "token": "5",
    "nome": "Natalia Moreti",
    "status": "I",
    "email": "<EMAIL>",
    "senha": "6fdd04d732fa9d9ca9910e59461a22cf0ac1620ea9d585bd0cd00913c2e26ea4",
    "imagem": "arquivos/aeceaf2a-a597-439f-b6ee-8e609494359d.jpg",
    "tipo": "user",
    "conexoes": [],
    "device_token": [],
    "__v": 0,
    "ultimo_status": "on",
    "perfil_permissoes": {
      "$oid": "60300eb3c9dd31761799a83b"
    },
    "siga_me": "I",
    "siga_me_fone": "",
    "tipo_acao_inatividade": "E",
    "genero": "",
    "updatedAt": {
      "$date": "2025-08-19T12:29:36.608Z"
    },
    "permiteAcessoApi": false,
    "departamento_ligacoes_efetuadas": {
      "$oid": "5bf73d1d186f7d2b0d647a60"
    },
    "perfilPermissoesApi": null,
    "siga_me_periodo": null,
    "siga_me_rota_saida": null,
    "online": "off"
  }