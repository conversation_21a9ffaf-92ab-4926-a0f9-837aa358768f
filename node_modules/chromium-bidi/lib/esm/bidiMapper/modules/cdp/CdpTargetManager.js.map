{"version": 3, "file": "CdpTargetManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/cdp/CdpTargetManager.ts"], "names": [], "mappings": "AAqBA,OAAO,EAAC,OAAO,EAAgB,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EACL,mBAAmB,EACnB,eAAe,GAChB,MAAM,mCAAmC,CAAC;AAM3C,OAAO,EAAC,WAAW,EAAuB,MAAM,0BAA0B,CAAC;AAG3E,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAEzC,MAAM,oBAAoB,GAAG;IAC3B,cAAc,EAAE,gBAAgB;IAChC,aAAa,EAAE,eAAe;IAC9B,MAAM,EAAE,kBAAkB;CAClB,CAAC;AAEX,MAAM,OAAO,gBAAgB;IAClB,iBAAiB,CAAY;IAC7B,cAAc,CAAgB;IAC9B,kCAAkC,GAAG,IAAI,GAAG,EAAU,CAAC;IACvD,aAAa,CAAS;IACtB,aAAa,CAAe;IAE5B,uBAAuB,CAAyB;IAChD,eAAe,CAAiB;IAChC,mBAAmB,CAAqB;IACxC,qBAAqB,CAAuB;IAC5C,aAAa,CAAe;IAC5B,cAAc,CAAuB;IAErC,qBAAqB,CAAsB;IAC3C,OAAO,CAAY;IAE5B,YACE,aAA4B,EAC5B,gBAA2B,EAC3B,YAAoB,EACpB,YAA0B,EAC1B,sBAA8C,EAC9C,YAA0B,EAC1B,cAA8B,EAC9B,aAAmC,EACnC,kBAAsC,EACtC,oBAA0C,EAC1C,oBAAyC,EACzC,MAAiB;QAEjB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,kCAAkC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,SAAoB;QACrC,SAAS,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QACH,SAAS,CAAC,EAAE,CACV,2BAA2B,EAC3B,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/C,CAAC;QACF,SAAS,CAAC,EAAE,CACV,0BAA0B,EAC1B,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9C,CAAC;QACF,SAAS,CAAC,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YAC3C,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,EAAE,CACV,oBAAoB,EACpB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1C,CAAC;QACF,SAAS,CAAC,EAAE,CACV,iCAAiC,EACjC,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,yBAAyB,CAAC,MAAwC;QAChE,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CACpE,MAAM,CAAC,aAAa,CACrB,CAAC;QACF,IAAI,qBAAqB,KAAK,SAAS,EAAE,CAAC;YACxC,mBAAmB,CAAC,MAAM,CACxB,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,aAAa,EACpB,qBAAqB,CAAC,WAAW,EACjC,qBAAqB,CAAC,SAAS,EAC/B,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc;YACnB,+EAA+E;YAC/E,SAAS;YACT,aAAa,EACb,SAAS,EACT,IAAI,CAAC,OAAO,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAED,iCAAiC,CAC/B,MAAqD;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1E,CAAC;IAED,4BAA4B,CAC1B,MAA6C,EAC7C,sBAAiC;QAEjC,MAAM,EAAC,SAAS,EAAE,UAAU,EAAC,GAAG,MAAM,CAAC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAEpE,MAAM,MAAM,GAAG,KAAK,IAAI,EAAE;YACxB,sDAAsD;YACtD,MAAM,eAAe;iBAClB,WAAW,CAAC,iCAAiC,CAAC;iBAC9C,IAAI,CAAC,GAAG,EAAE,CACT,sBAAsB,CAAC,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtE;iBACA,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC;QAEF,sCAAsC;QACtC,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC/C,KAAK,MAAM,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,8DAA8D;QAC9D,kEAAkE;QAClE,+DAA+D;QAC/D,6DAA6D;QAC7D,gDAAgD;QAChD,MAAM,SAAS,GACb,UAAU,CAAC,IAAI,KAAK,gBAAgB;YAClC,CAAC,CAAC,GAAG,sBAAsB,CAAC,SAAS,IAAI,UAAU,CAAC,QAAQ,EAAE;YAC9D,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;QAE1B,4DAA4D;QAC5D,iEAAiE;QACjE,mCAAmC;QACnC,IAAI,IAAI,CAAC,kCAAkC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3D,yCAAyC;YACzC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,kCAAkC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEvD,MAAM,WAAW,GACf,UAAU,CAAC,gBAAgB;YAC3B,UAAU,CAAC,gBAAgB,KAAK,IAAI,CAAC,qBAAqB;YACxD,CAAC,CAAC,UAAU,CAAC,gBAAgB;YAC7B,CAAC,CAAC,SAAS,CAAC;QAEhB,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;YACxB,KAAK,KAAK,CAAC,CAAC,CAAC;gBACX,qEAAqE;gBACrE,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;gBAEzC,iFAAiF;gBACjF,kFAAkF;gBAClF,UAAU;gBACV,KAAK,CAAC,KAAK,IAAI,EAAE;oBACf,MAAM,eAAe,CAAC,WAAW,CAAC,sBAAsB,EAAE;wBACxD,UAAU,EAAE,IAAI;wBAChB,sBAAsB,EAAE,IAAI;wBAC5B,OAAO,EAAE,IAAI;qBACd,CAAC,CAAC;gBACL,CAAC,CAAC,EAAE,CAAC;gBACL,OAAO;YACT,CAAC;YACD,KAAK,MAAM,CAAC;YACZ,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CACrC,eAAe,EACf,sBAAsB,EACtB,UAAU,EACV,WAAW,CACZ,CAAC;gBACF,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAC3D,UAAU,CAAC,QAAQ,CACpB,CAAC;gBACF,IAAI,YAAY,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACjD,SAAS;oBACT,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,gFAAgF;oBAChF,qDAAqD;oBACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CACtC,UAAU,EACV,sBAAsB,CAAC,SAAS,CACjC,CAAC;oBACF,eAAe;oBACf,mBAAmB,CAAC,MAAM,CACxB,UAAU,CAAC,QAAQ,EACnB,QAAQ,EACR,WAAW,EACX,SAAS,EACT,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc;oBACnB,6EAA6E;oBAC7E,4EAA4E;oBAC5E,2EAA2E;oBAC3E,6EAA6E;oBAC7E,+CAA+C;oBAC/C,wCAAwC;oBACxC,4FAA4F;oBAC5F,yEAAyE;oBACzE,UAAU,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,EACtD,UAAU,CAAC,aAAa,IAAI,UAAU,CAAC,QAAQ,EAC/C,IAAI,CAAC,OAAO,CACb,CAAC;gBACJ,CAAC;gBACD,OAAO;YACT,CAAC;YACD,KAAK,gBAAgB,CAAC;YACtB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;oBACzC,YAAY,EAAE,sBAAsB,CAAC,SAAS;iBAC/C,CAAC,CAAC;gBACH,sEAAsE;gBACtE,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,KAAK,MAAM,EAAE,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CACrC,eAAe,EACf,sBAAsB,EACtB,UAAU,EACV,WAAW,CACZ,CAAC;gBACF,IAAI,CAAC,mBAAmB,CACtB,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,EACrC,SAAS,EACT,KAAK,CACN,CAAC;gBACF,OAAO;YACT,CAAC;YACD,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzE,yEAAyE;YACzE,KAAK,eAAe,CAAC,CAAC,CAAC;gBACrB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CACrC,eAAe,EACf,sBAAsB,EACtB,UAAU,EACV,WAAW,CACZ,CAAC;gBACF,IAAI,CAAC,mBAAmB,CACtB,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,EACrC,SAAS,CACV,CAAC;gBACF,OAAO;YACT,CAAC;QACH,CAAC;QAED,oEAAoE;QACpE,4BAA4B;QAC5B,KAAK,MAAM,EAAE,CAAC;IAChB,CAAC;IAED,gFAAgF;IAChF,kBAAkB,CAChB,UAAsC,EACtC,eAAsD;QAEtD,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,IAAI,UAAU,CAAC,QAAQ,CAAC;QACjE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,OAAO,CACL,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,eAAe,CAAC;gBAChE,EAAE,EAAE,IAAI,IAAI,CACf,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gBAAgB,CACd,eAA0B,EAC1B,eAA0B,EAC1B,UAAsC,EACtC,WAAgC;QAEhC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QACzC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAC3C,UAAU,CAAC,QAAQ,EACnB,WAAW,CACZ,CAAC;QAEF,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAC7B,UAAU,CAAC,QAAQ,EACnB,eAAe,EACf,IAAI,CAAC,iBAAiB,EACtB,eAAe,EACf,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,EACnB,WAAW,EACX,IAAI,CAAC,OAAO,CACb,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEpD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,QAAQ,GAAG,IAAI,GAAG,EAAiB,CAAC;IACpC,mBAAmB,CACjB,SAA0B,EAC1B,SAAoB,EACpB,UAAkB;QAElB,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,iCAAiC,EAAE,CAAC,MAAM,EAAE,EAAE;YACnE,MAAM,EAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAC,GAAG,MAAM,CAAC,OAAO,CAAC;YAC9C,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,SAAS,CAAC,SAAS,EACnB,IAAI,CAAC,aAAa,EAClB,EAAE,EACF,IAAI,CAAC,OAAO,EACZ,eAAe,CAAC,MAAM,CAAC,EACvB,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,EAC9B,QAAQ,EACR,IAAI,CAAC,aAAa,EAClB,SAAS,CACV,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8BAA8B,CAAC,EAC7B,SAAS,EACT,QAAQ,GACgC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;gBAChE,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC;QACD,MAAM,OAAO,GACX,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAC/D,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;gBAC9B,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,SAAS;aACzC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,6BAA6B,CAC3B,MAA8C;QAE9C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CACtD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAC3B,CAAC;QACF,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,yBAAyB,CAAC,SAAoB;QAC5C,0EAA0E;QAC1E,wEAAwE;QACxE,mBAAmB;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;YAC3C,YAAY,EAAE,SAAS,CAAC,SAAS;SAClC,CAAC,CAAC;QACH,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,KAAK,CAAC,OAAO,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;CACF"}