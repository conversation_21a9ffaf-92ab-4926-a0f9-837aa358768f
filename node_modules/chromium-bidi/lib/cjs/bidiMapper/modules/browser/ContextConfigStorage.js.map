{"version": 3, "file": "ContextConfigStorage.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/browser/ContextConfigStorage.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,yDAAiD;AAEjD;;;;;;;;;;;;;GAaG;AACH,MAAa,oBAAoB;IAC/B,OAAO,GAAG,IAAI,gCAAa,EAAE,CAAC;IAC9B,mBAAmB,GAAG,IAAI,GAAG,EAAyB,CAAC;IACvD,uBAAuB,GAAG,IAAI,GAAG,EAAyB,CAAC;IAE3D;;;OAGG;IACH,kBAAkB,CAAC,MAAqB;QACtC,IAAI,CAAC,OAAO,GAAG,gCAAa,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACH,2BAA2B,CACzB,iBAAyB,EACzB,MAAqB;QAErB,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAC9B,iBAAiB,EACjB,gCAAa,CAAC,KAAK,CACjB,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EACnD,MAAM,CACP,CACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,uBAAuB,CAAC,WAAmB,EAAE,MAAqB;QAChE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAC1B,WAAW,EACX,gCAAa,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,CACvE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,yBAAiC,EAAE,WAAmB;QACpE,OAAO,gCAAa,CAAC,KAAK,CACxB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EACzC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAC5D,CAAC;IACJ,CAAC;CACF;AA3DD,oDA2DC"}